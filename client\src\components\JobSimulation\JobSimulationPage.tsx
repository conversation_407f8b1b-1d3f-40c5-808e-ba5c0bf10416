import { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons';
import { AnimatePresence, motion } from 'framer-motion';
import { memo, useEffect, useRef, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useRecoilState, useRecoilValue } from 'recoil';
import { TJobSimulationContext } from '~/common';
import store from '~/store';
import ChatWithAssistantRoot from './Chat/ChatWithAssistantRoot';
import JobSimulationHeaderSettings from './Settings/JobSimulationHeaderSettings';
import VirtualWorld from './VirtualWorld';
import WorkPortal from './WorkPortal/WorkPortal';

function JobSimulationPage() {
  const { jobSimulationData } = useOutletContext<TJobSimulationContext>();
  const [chatWidth, setChatWidth] = useState(100);
  const [isWorkPortalVisible, setIsWorkPortalVisible] = useState(false);
  const [jobSimulationIframe, setJobSimulationIframe] = useRecoilState(store.jobSimulationIframe);
  const isDraggingRef = useRef(false);
  const isTogglingRef = useRef(false);
  // const isSubmittingFamily = useRecoilValue(store.isSubmittingFamily(0));
  const shouldShowGlow = useRecoilValue(store.shouldShowGlowState);
  const glowRef = useRef<HTMLDivElement>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [audioCtx] = useState(() => new AudioContext());
  const [sourceNode] = useState(() =>
    audioCtx.createMediaElementSource(new Audio('/assets/sounds/typing.mp3')),
  );
  const analyserRef = useRef<any | null>(null);
  const [isOpenWorkPortal] = useRecoilState(store.jobSimulationIsOpenWorkPortal);

  const handleMouseDown = () => {
    isDraggingRef.current = true;
    isTogglingRef.current = false;
    document.body.style.cursor = 'col-resize';
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDraggingRef.current) return;
    const newChatWidth = (e.clientX / window.innerWidth) * 100;
    if (newChatWidth >= 20 && newChatWidth <= 70) {
      setChatWidth(newChatWidth);
    }
  };

  const handleMouseUp = () => {
    isDraggingRef.current = false;
    document.body.style.cursor = 'default';
  };

  const toggleWorkPortal = (value?: boolean) => {
    isTogglingRef.current = true;
    const toggleValue = value ?? !isWorkPortalVisible;
    setIsWorkPortalVisible(toggleValue);
    if (toggleValue) {
      setChatWidth(30); // Quay lại fullscreen cho ChatWithAssistantRoot khi ẩn WorkPortal
    } else {
      setChatWidth(100); // Khi mở WorkPortal, ChatWithAssistantRoot sẽ thu hẹp xuống 30%
    }
  };

  const closeIframe = () => {
    // setIframeUrl(null);
    // setIframeVisible(false);
    setJobSimulationIframe(null);
  };

  useEffect(() => {
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  useEffect(() => {
    toggleWorkPortal(isOpenWorkPortal);
  }, [isOpenWorkPortal]);

  useEffect(() => {
    audioRef.current = sourceNode.mediaElement;
    audioRef.current.loop = true;
    audioRef.current.volume = 0.5;

    const analyser = audioCtx.createAnalyser();
    sourceNode.connect(analyser);
    analyser.connect(audioCtx.destination);
    analyserRef.current = analyser;
  }, []);

  useEffect(() => {
    const analyser = analyserRef.current;
    const dataArray = new Uint8Array(analyser?.frequencyBinCount || 0);

    const animate = () => {
      if (!analyser || !glowRef.current) return;

      analyser.getByteFrequencyData(dataArray);
      const avg = dataArray.reduce((sum, v) => sum + v, 0) / dataArray.length;

      const scale = 1 + (avg / 256) * 0.5;
      glowRef.current.style.transform = `scale(${scale})`;
      glowRef.current.style.filter = `blur(${(avg / 255) * 10}px)`;

      requestAnimationFrame(animate);
    };

    if (shouldShowGlow) {
      audioRef.current?.play().catch(console.warn);
      audioCtx.resume();
      animate();
    } else {
      audioRef.current?.pause();
      audioRef.current!.currentTime = 0;
    }
  }, [shouldShowGlow]);

  if (!jobSimulationData) return null;

  return (
    <>
      <div className="relative flex h-screen flex-row">
        <motion.div
          className="relative bg-white dark:bg-gray-900"
          animate={{ width: chatWidth + '%' }}
          transition={
            isTogglingRef.current
              ? { type: 'spring', stiffness: 150, damping: 25 }
              : { duration: 0 }
          }
        >
          <div className="absolute left-0 top-0 flex h-[30px] max-h-[30px] w-full justify-end gap-2 p-1">
            <JobSimulationHeaderSettings />
          </div>
          <div className="h-full pt-[30px]">
            <ChatWithAssistantRoot />
          </div>
        </motion.div>

        {/* Resize button */}
        {isWorkPortalVisible && (
          <button
            onMouseDown={handleMouseDown}
            className="z-10 flex h-full w-2 cursor-col-resize items-center justify-center bg-gray-300 transition-colors hover:bg-gray-400"
            aria-label="Resize panel"
          />
        )}

        {/* WorkPortal animation */}
        <motion.div
          className="relative flex-grow overflow-x-hidden"
          initial={{ x: '100%' }}
          animate={{ x: isWorkPortalVisible ? '0%' : '100%' }}
          exit={{ x: '100%' }}
          transition={
            isTogglingRef.current
              ? { type: 'spring', stiffness: 150, damping: 25 }
              : { duration: 0 }
          }
          style={{ willChange: 'transform' }}
        >
          <img src="/assets/job-simulation/background.jpg" className="absolute h-full w-full" />
          <div className="absolute bottom-0 right-0 top-0 w-full">
            <WorkPortal />
          </div>
          <AnimatePresence>
            {jobSimulationIframe?.visible && jobSimulationIframe?.url && (
              <motion.div
                key="iframe"
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                className="absolute top-0 z-30 flex h-full w-full items-center justify-center bg-white"
              >
                <div className="relative h-full w-full rounded-xl border">
                  <button
                    className="absolute right-2 top-2 rounded-md bg-[rgba(255,255,255,0.7)]"
                    onClick={closeIframe}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-x-icon lucide-x"
                    >
                      <path d="M18 6 6 18" />
                      <path d="m6 6 12 12" />
                    </svg>
                  </button>
                  {jobSimulationIframe.type === 'virtual-world' ? (
                    <VirtualWorld url={jobSimulationIframe.url} />
                  ) : (
                    <iframe
                      src={jobSimulationIframe?.url ?? undefined}
                      className="h-full w-full border-0"
                      allow="camera; microphone; display-capture; clipboard-read; clipboard-write; fullscreen"
                    />
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      <button
        onClick={() => toggleWorkPortal()}
        className="absolute right-2 top-1/2 z-10 h-10 -translate-y-1/2 rounded-lg bg-gray-300 shadow-xl"
        aria-label={isWorkPortalVisible ? 'Hide WorkPortal' : 'Show WorkPortal'}
      >
        {isWorkPortalVisible ? <ChevronRightIcon /> : <ChevronLeftIcon />}
      </button>
    </>
  );
}

export default memo(JobSimulationPage);
