// TODO: fetch data from db

import { AppSimulationScreen } from '~/common';
import { CalculationConfig } from '~/components/JobSimulation/AppSimulation/CalculationEngine';

// Facebook Ad Calculation Configuration
export const facebookAdCalculationConfig: CalculationConfig = {
  rules: {
    estimated_accounts: {
      id: 'estimated_accounts',
      type: 'range',
      inputs: ['budget', 'audience_location', 'audience_age', 'audience_gender'],
      baseValue: 500,
      multipliers: {
        budget: { type: 'linear', factor: 1 },
        audience_location: { type: 'array_length', factor: 1.5 },
        audience_age: { type: 'range_width', maxRange: 47, minValue: 0.6 },
        audience_gender: {
          type: 'conditional',
          conditions: [
            { condition: 'value === "All"', value: 1.5 },
            { condition: 'value === "Men"', value: 0.8 },
            { condition: 'value === "Women"', value: 0.7 },
          ],
        },
      },
      formatters: [{ type: 'number' }],
    },
    estimated_post_engagement: {
      id: 'estimated_post_engagement',
      type: 'range',
      inputs: ['budget', 'audience_location', 'audience_age', 'audience_gender'],
      baseValue: 100,
      multipliers: {
        budget: { type: 'linear', factor: 1 },
        audience_location: { type: 'array_length', factor: 1.5 },
        audience_age: { type: 'range_width', maxRange: 47, minValue: 0.6 },
        audience_gender: {
          type: 'conditional',
          conditions: [
            { condition: 'value === "All"', value: 1.5 },
            { condition: 'value === "Men"', value: 0.8 },
            { condition: 'value === "Women"', value: 0.7 },
          ],
        },
      },
      formatters: [{ type: 'number' }],
    },
    total_budget: {
      id: 'total_budget',
      type: 'formula',
      inputs: ['budget'],
      formula: 'budget * 7',
      formatters: [{ type: 'currency' }],
    },
    budget_per_day: {
      id: 'budget_per_day',
      type: 'formula',
      inputs: ['budget'],
      formula: 'budget',
      formatters: [{ type: 'custom', template: '${value}.00 a day x 7 days' }],
    },
    estimated_vat: {
      id: 'estimated_vat',
      type: 'formula',
      inputs: ['total_budget_raw'],
      formula: 'total_budget_raw * 0.05',
      formatters: [{ type: 'currency' }],
    },
    total_budget_raw: {
      id: 'total_budget_raw',
      type: 'formula',
      inputs: ['budget'],
      formula: 'budget * 7',
    },
    total_amount: {
      id: 'total_amount',
      type: 'formula',
      inputs: ['total_budget_raw', 'estimated_vat_raw'],
      formula: 'total_budget_raw + estimated_vat_raw',
      formatters: [{ type: 'currency' }],
    },
    estimated_vat_raw: {
      id: 'estimated_vat_raw',
      type: 'formula',
      inputs: ['total_budget_raw'],
      formula: 'total_budget_raw * 0.05',
    },
  },
  dependencies: {
    budget: [
      'estimated_accounts',
      'estimated_post_engagement',
      'total_budget',
      'budget_per_day',
      'total_budget_raw',
    ],
    audience_location: ['estimated_accounts', 'estimated_post_engagement'],
    audience_age: ['estimated_accounts', 'estimated_post_engagement'],
    audience_gender: ['estimated_accounts', 'estimated_post_engagement'],
    total_budget_raw: ['estimated_vat', 'estimated_vat_raw', 'total_amount'],
    estimated_vat_raw: ['total_amount'],
  },
  defaultValues: {
    budget: 1,
    audience_location: ['Singapore'],
    audience_age: [18, 65],
    audience_gender: 'All',
  },
};

const budgetDropdownOptions = [
  {
    label: '$1.00',
    value: 1,
    screenId: '005',
    dataContextId: 'budget',
    dataContext: 'Budget selected: $1.00 per day',
    saveToSelections: true,
  },
  {
    label: '$10.00',
    value: 10,
    screenId: '005_03',
    dataContextId: 'budget',
    dataContext: 'Budget selected: $10.00 per day',
    saveToSelections: true,
  },
];

export const fbAdScreens: AppSimulationScreen[] = [
  {
    id: '001',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/001.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Create Ad',
        x1: 55.4,
        y1: 43.25,
        x2: 59.2,
        y2: 46.2,
        action: { type: 'nextScreen' },
      },
      {
        title: 'Create Ad',
        x1: 95.3,
        y1: 1.3,
        x2: 99.2,
        y2: 4.3,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '002',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/002.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Choose a goal:',
        x1: 62.2,
        y1: 12.3,
        x2: 69.8,
        y2: 30.97,
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '003',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/003.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Create post',
        x1: 46.8,
        y1: 40.5,
        x2: 51.1,
        y2: 43.5,
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '004',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/004.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Add photo',
        x1: 5.64,
        y1: 38.99,
        x2: 12.76,
        y2: 43.06,
        action: { type: 'uploadPhoto', dataContextId: 'postImage' },
      },
      {
        title: "Post's text",
        x1: 5.64,
        y1: 55.86,
        x2: 38.72,
        y2: 72.45,
        action: {
          type: 'inputText',
          inputTextType: 'textarea',
          dataContextId: 'postText',
          dataContextLabel: "Post's text",
          saveToSelections: true,
        },
      },
      {
        title: 'Publish',
        x1: 33.92,
        y1: 92.39,
        x2: 38.68,
        y2: 96.52,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen' },
      },
      {
        title: 'Button (unnamed)',
        x1: 22.58,
        y1: 92.54,
        x2: 26.99,
        y2: 96.54,
        action: { type: 'nextScreen', screenId: '003' },
      },
    ],
    placeholders: [
      {
        id: 'imageSmall',
        type: 'image',
        dataId: 'postImage',
        x1: 5.57,
        y1: 30.7,
        x2: 8.43,
        y2: 36.31,
        title: 'Image small placeholder',
      },
      {
        id: 'imageLarge',
        type: 'image',
        dataId: 'postImage',
        x1: 55.15,
        y1: 31.6,
        x2: 84.2,
        y2: 88.4,
        title: 'Image large placeholder',
      },
      {
        id: 'text',
        type: 'text',
        dataId: 'postText',
        x1: 56.14,
        y1: 22.02,
        x2: 82.93,
        y2: 29.81,
        title: 'Text placeholder',
        style: {
          alignItems: 'start',
        },
        responsiveConfig: {
          baseFontSize: 0.9,
          maxFontSize: 0.9,
          scaleWithImage: true,
        },
      },
    ],
  },
  {
    id: '005',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/005_budget01.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Budget',
        x1: 9.49367088607595,
        y1: 61.54844336709283,
        x2: 37.18354430379747,
        y2: 67.6228152947973,
        action: {
          type: 'dropdown',
          dropdownOptions: budgetDropdownOptions,
        },
      },
      {
        title: 'Audience',
        x1: 39.200949367088604,
        y1: 20.741124263026954,
        x2: 41.25791139240506,
        y2: 24.47919929546047,
        action: {
          type: 'modal',
          modalConfig: {
            title: 'Audience Settings',
            description: 'Configure your audience targeting',
            inputs: [
              {
                id: 'location',
                type: 'multiSelect',
                label: 'Location',
                dataId: 'audience_location',
                options: ['Vietnam', 'HongKong', 'China', 'Australia', 'Singapore'],
                required: true,
                minSelections: 1,
                defaultValue: ['Singapore'],
              },
              {
                id: 'age',
                type: 'range',
                label: 'Age Range',
                dataId: 'audience_age',
                min: 18,
                max: 65,
                defaultValue: [18, 65],
                labels: { min: '18', max: '65+' },
                formatType: 'age-range',
                formatConfig: {
                  maxValue: 50,
                  maxLabel: '65+',
                  separator: ' - ',
                },
              },
              {
                id: 'gender',
                type: 'radio',
                label: 'Gender',
                dataId: 'audience_gender',
                radioOptions: [
                  { value: 'All', label: 'All' },
                  { value: 'Men', label: 'Men' },
                  { value: 'Women', label: 'Women' },
                ],
                defaultValue: 'All',
              },
            ],
          },
        },
      },
      {
        title: 'Publish Ad',
        x1: 73.41772151898735,
        y1: 94.80173584311599,
        x2: 78.1,
        y2: 99.2,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        // action: {
        //   // type: 'triggerMessage',
        //   // message: "I've published the ad with the following settings:",
        //   // withData: true,
        //   type: 'nextScreen',
        //   screenId: '006',
        // },
        actions: [
          {
            type: 'triggerMessage',
            message: "I've published the ad with the following settings:",
            withData: true,
          },
          {
            type: 'nextScreen',
            screenId: '006',
          },
        ],
      },
      {
        title: 'Cancel',
        x1: 68.63132911392405,
        y1: 94.95748778116534,
        x2: 73,
        y2: 99,
        action: { type: 'nextScreen', screenId: '004' },
      },
    ],
    placeholders: [
      {
        id: 'audience_location',
        type: 'text',
        dataId: 'audience_location',
        x1: 9.***************,
        y1: 24.86858211133896,
        x2: 38.52848101265822,
        y2: 27.282755569785604,
        title: 'Audience Location',
        initialValue: 'Singapore',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'audience_age',
        type: 'text',
        dataId: 'audience_age',
        x1: 9.25632911392405,
        y1: 27.6,
        x2: 15,
        y2: 30.014,
        title: 'Audience Age',
        initialValue: '18 - 65+',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'audience_gender',
        type: 'text',
        dataId: 'audience_gender',
        x1: 9.***************,
        y1: 30.3,
        x2: 13.***************,
        y2: 32.714,
        title: 'Audience Gender',
        initialValue: 'All',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_accounts',
        type: 'text',
        dataId: 'estimated_accounts',
        x1: 45.**************,
        y1: 11.***************,
        x2: 58.**************,
        y2: 14.**************,
        title: 'Estimated Accounts Reached',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_post_engagement',
        type: 'text',
        dataId: 'estimated_post_engagement',
        x1: 45.***************,
        y1: 20.***************,
        x2: 58.**************,
        y2: 24.***************,
        title: 'Estimated Posts Engagement',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'total_budget',
        type: 'text',
        dataId: 'total_budget',
        x1: 54.**************,
        y1: 40.***************,
        x2: 62.**************,
        y2: 43.**************,
        title: 'Total Budget',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
      {
        id: 'budget_per_day',
        type: 'text',
        x1: 45.***************,
        y1: 42.85806820492525,
        x2: 54.153481012658234,
        y2: 46.59614323735877,
        dataId: 'budget_per_day',
        title: 'Budget Per Day',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_vat',
        type: 'text',
        x1: 54.**************,
        y1: 46.751896363710166,
        x2: 62.65822784810127,
        y2: 49.5554526380353,
        dataId: 'estimated_vat',
        title: 'Estimated VAT',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
      {
        id: 'total_amount',
        type: 'text',
        x1: 54.90506329113924,
        y1: 51.42449015425207,
        x2: 62.69778481012657,
        y2: 54.695305807631396,
        dataId: 'total_amount',
        title: 'Total Amount',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
    ],
  },
  {
    id: '005_03',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/005_budget03.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Budget',
        x1: 9.49367088607595,
        y1: 61.54844336709283,
        x2: 37.18354430379747,
        y2: 67.6228152947973,
        action: {
          type: 'dropdown',
          dropdownOptions: budgetDropdownOptions,
        },
      },
      {
        title: 'Audience',
        x1: 39.200949367088604,
        y1: 20.741124263026954,
        x2: 41.25791139240506,
        y2: 24.47919929546047,
        action: {
          type: 'modal',
          modalConfig: {
            title: 'Audience Settings',
            description: 'Configure your audience targeting',
            inputs: [
              {
                id: 'location',
                type: 'multiSelect',
                label: 'Location',
                dataId: 'audience_location',
                options: ['Vietnam', 'HongKong', 'China', 'Australia', 'Singapore'],
                required: true,
                minSelections: 1,
                defaultValue: ['Singapore'],
              },
              {
                id: 'age',
                type: 'range',
                label: 'Age Range',
                dataId: 'audience_age',
                min: 18,
                max: 65,
                defaultValue: [18, 65],
                labels: { min: '18', max: '65+' },
                formatType: 'age-range',
                formatConfig: {
                  maxValue: 50,
                  maxLabel: '65+',
                  separator: ' - ',
                },
              },
              {
                id: 'gender',
                type: 'radio',
                label: 'Gender',
                dataId: 'audience_gender',
                radioOptions: [
                  { value: 'All', label: 'All' },
                  { value: 'Men', label: 'Men' },
                  { value: 'Women', label: 'Women' },
                ],
                defaultValue: 'All',
              },
            ],
          },
        },
      },
      {
        title: 'Publish Ad',
        x1: 73.41772151898735,
        y1: 94.80173584311599,
        x2: 78.1,
        y2: 99.2,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        // action: {
        //   type: 'nextScreen',
        //   screenId: '006',
        // },
        actions: [
          {
            type: 'triggerMessage',
            message: "I've published the ad with the following settings:",
            withData: true,
          },
          {
            type: 'nextScreen',
            screenId: '006',
          },
        ],
      },
      {
        title: 'Cancel',
        x1: 68.63132911392405,
        y1: 94.95748778116534,
        x2: 73,
        y2: 99,
        action: { type: 'nextScreen', screenId: '004' },
      },
    ],
    placeholders: [
      {
        id: 'audience_location',
        type: 'text',
        dataId: 'audience_location',
        x1: 9.***************,
        y1: 24.86858211133896,
        x2: 38.52848101265822,
        y2: 27.282755569785604,
        title: 'Audience Location',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'audience_age',
        type: 'text',
        dataId: 'audience_age',
        x1: 9.25632911392405,
        y1: 27.750014948839798,
        x2: 13.409810126582278,
        y2: 29.774805591407947,
        title: 'Audience Age',
        initialValue: '18 - 65+',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'audience_gender',
        type: 'text',
        dataId: 'audience_gender',
        x1: 9.***************,
        y1: 30.***************,
        x2: 13.***************,
        y2: 32.**************,
        title: 'Audience Gender',
        initialValue: 'All',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_accounts',
        type: 'text',
        dataId: 'estimated_accounts',
        x1: 45.**************,
        y1: 11.***************,
        x2: 58.**************,
        y2: 14.**************,
        title: 'Estimated Accounts Reached',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_post_engagement',
        type: 'text',
        dataId: 'estimated_post_engagement',
        x1: 45.***************,
        y1: 20.***************,
        x2: 58.**************,
        y2: 24.***************,
        title: 'Estimated Posts Engagement',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'total_budget',
        type: 'text',
        dataId: 'total_budget',
        x1: 54.**************,
        y1: 40.***************,
        x2: 62.**************,
        y2: 43.**************,
        title: 'Total Budget',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
      {
        id: 'budget_per_day',
        type: 'text',
        x1: 45.***************,
        y1: 42.85806820492525,
        x2: 54.153481012658234,
        y2: 46.59614323735877,
        dataId: 'budget_per_day',
        title: 'Budget Per Day',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_vat',
        type: 'text',
        x1: 54.**************,
        y1: 46.751896363710166,
        x2: 62.65822784810127,
        y2: 49.5554526380353,
        dataId: 'estimated_vat',
        title: 'Estimated VAT',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
      {
        id: 'total_amount',
        type: 'text',
        x1: 54.90506329113924,
        y1: 51.42449015425207,
        x2: 62.69778481012657,
        y2: 54.695305807631396,
        dataId: 'total_amount',
        title: 'Total Amount',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
    ],
  },
  {
    id: '006',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/006.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'View Post',
        x1: 63.005050505050505,
        y1: 91.15662649319022,
        x2: 96.11742424242425,
        y2: 95.27733913408223,
        action: { type: 'nextScreen', screenId: '007' },
      },
    ],
    charts: [
      {
        id: 'chart1',
        type: 'line',
        data: {
          datasets: [
            { x: 10.04, y: 67.41, label: '10k', time: 6 },
            { x: 14.38, y: 65.8, label: '15k', time: 9 },
            { x: 19.18, y: 63.73, label: '50k', time: 12 },
            { x: 24.12, y: 61.89, label: '60k', time: 15 },
            { x: 28.31, y: 60.97, label: '70k', time: 24 },
            { x: 35.8, y: 60.28, label: '75k', time: 48 },
            { x: 37.3, y: 54.99, label: '300k', time: 60 },
            { x: 46.44, y: 53.15, label: '400k', time: 90 },
            { x: 55.88, y: 52.0, label: '500k', time: 120 },
          ],
          chartArea: {
            left: 3.89,
            top: 43.25,
            width: 55.73,
            height: 29.22,
          },
        },
      },
    ],
    placeholders: [
      {
        id: 'imageSmall',
        type: 'image',
        dataId: 'postImage',
        x1: 6.05,
        y1: 1.71,
        x2: 9.87,
        y2: 7.33,
        title: 'Image small placeholder',
      },
      {
        id: 'imageLarge',
        type: 'image',
        dataId: 'postImage',
        x1: 63.9,
        y1: 35.77,
        x2: 95.34,
        y2: 77.8,
        title: 'Image large placeholder',
      },
      {
        id: 'postText1',
        type: 'text',
        dataId: 'postText',
        x1: 10.35,
        y1: 1.96,
        x2: 96.14,
        y2: 5.87,
        title: 'Post text placeholder',
        style: {
          // whiteSpace: 'unset',
          backgroundColor: 'unset',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
      },
      {
        id: 'postText2',
        type: 'text',
        dataId: 'postText',
        x1: 64.94,
        y1: 23.71,
        x2: 93.59,
        y2: 31.54,
        title: 'Post text placeholder',
        style: {
          alignItems: 'start',
        },
        responsiveConfig: {
          baseFontSize: 1,
          maxFontSize: 1,
          scaleWithImage: true,
        },
      },
      {
        id: 'view1',
        type: 'text',
        x1: 3.93,
        y1: 18.33,
        x2: 9.17,
        y2: 22.58,
        title: 'Post views placeholder',
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          fontWeight: 'bold',
          border: '0px',
        },
        dataByTime: [
          { time: 0, value: '0' },
          { time: 6, value: '10K' },
          { time: 9, value: '15K' },
          { time: 12, value: '50K' },
          { time: 15, value: '60K' },
          { time: 24, value: '70K' },
          { time: 48, value: '75K' },
          { time: 60, value: '300K' },
          { time: 90, value: '400K' },
          { time: 120, value: '500K' },
        ],
      },
      {
        id: 'view2',
        type: 'text',
        x1: 3.78,
        y1: 31.52,
        x2: 9.17,
        y2: 36.66,
        title: 'Post views placeholder',
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          fontWeight: 'bold',
          border: '0px',
        },
        dataByTime: [
          { time: 0, value: '0' },
          { time: 6, value: '10K' },
          { time: 9, value: '15K' },
          { time: 12, value: '50K' },
          { time: 15, value: '60K' },
          { time: 24, value: '70K' },
          { time: 48, value: '75K' },
          { time: 60, value: '300K' },
          { time: 90, value: '400K' },
          { time: 120, value: '500K' },
        ],
      },
      {
        id: 'reactions',
        type: 'text',
        x1: 3.64,
        y1: 81.37,
        x2: 11.21,
        y2: 86.29,
        title: 'Post reactions placeholder',
        dataId: 'postReactions',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          fontWeight: 'bold',
          border: '0px',
        },
      },
      {
        id: 'comments',
        type: 'text',
        x1: 18.63,
        y1: 81.6,
        x2: 26.2,
        y2: 85.62,
        title: 'Post comments placeholder',
        dataId: 'postComments',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          fontWeight: 'bold',
          border: '0px',
        },
      },
      {
        id: 'shares',
        type: 'text',
        x1: 33.77,
        y1: 81.15,
        x2: 40.32,
        y2: 85.85,
        title: 'Post shares placeholder',
        dataId: 'postShares',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          fontWeight: 'bold',
          border: '0px',
        },
      },
    ],
    // actions: [
    //   {
    //     type: 'triggerMessage',
    //     message: "I've published the ad with the following settings:",
    //     withData: true,
    //   },
    // ],
  },
  {
    id: '007',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/newsfeed.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Ad',
        x1: 34.02777777777778,
        y1: 5.504984354216049,
        x2: 66.41414141414141,
        y2: 99.3989871598336,
        action: { type: 'nextScreen', screenId: '006' },
      },
    ],
    placeholders: [
      {
        id: 'imageSmall',
        type: 'image',
        dataId: 'postImage',
        x1: 34.6123417721519,
        y1: 6.666150037010931,
        x2: 36.550632911392405,
        y2: 10.541818663180079,
        title: 'Image small placeholder',
      },
      {
        id: 'imageLarge',
        type: 'image',
        dataId: 'postImage',
        x1: 34.05854430379747,
        y1: 23.331525129538264,
        x2: 66.29746835443038,
        y2: 86.42741036357197,
        title: 'Image large placeholder',
      },
      {
        id: 'companyName',
        type: 'text',
        dataId: 'companyName',
        x1: 36.82753164556962,
        y1: 6.743663409534315,
        x2: 46.75632911392405,
        y2: 8.759011095142272,
        title: 'Company text placeholder',
        initialValue: 'BrightWave Media',
        style: {
          alignItems: 'start',
          whiteSpace: 'unset',
          backgroundColor: 'unset',
          fontWeight: 'bold',
        },
      },
      {
        id: 'postHeadline',
        type: 'text',
        dataId: 'postHeadline',
        x1: 34.05854430379747,
        y1: 86.63411426731459,
        x2: 59.6123417721519,
        y2: 92.21507708899817,
        title: 'Post headline placeholder',
        initialValue: 'BrightWave Media',
        style: {
          alignItems: 'center',
          whiteSpace: 'unset',
          backgroundColor: 'unset',
          fontWeight: 'bold',
        },
      },
      {
        id: 'postText1',
        type: 'text',
        dataId: 'postText',
        x1: 34.651898734177216,
        y1: 11.084412270843758,
        x2: 65.70411392405063,
        y2: 22.55639140430443,
        title: 'Post text placeholder',
        style: {
          alignItems: 'start',
          backgroundColor: 'unset',
        },
        responsiveConfig: {
          baseFontSize: 1,
          maxFontSize: 1,
          scaleWithImage: true,
        },
      },
      {
        id: 'reactions',
        type: 'text',
        x1: 35.60126582278481,
        y1: 92.5251305790917,
        x2: 45.625,
        y2: 95.31561198993347,
        title: 'Post reactions placeholder',
        dataId: 'postReactions',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          // fontSize: '0.85rem',
          backgroundColor: 'unset',
          color: '#000',
          border: '0px',
        },
        responsiveConfig: {
          baseFontSize: 1,
          maxFontSize: 1,
          scaleWithImage: true,
        },
      },
      {
        id: 'comments',
        type: 'text',
        x1: 47,
        y1: 92.68015732413846,
        x2: 57.83227848101266,
        y2: 95.31561198993347,
        title: 'Post comments placeholder',
        dataId: 'postComments',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          // fontSize: '0.85rem',
          backgroundColor: 'unset',
          color: '#000',
          border: '0px',
          justifyContent: 'right',
        },
        responsiveConfig: {
          baseFontSize: 1,
          maxFontSize: 1,
          scaleWithImage: true,
        },
      },
      {
        id: 'shares',
        type: 'text',
        x1: 61.07594936708861,
        y1: 92.75767069666185,
        x2: 63.88449367088608,
        y2: 95.16058524488672,
        title: 'Post shares placeholder',
        dataId: 'postShares',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          // fontSize: '0.85rem',
          backgroundColor: 'unset',
          color: '#000',
          border: '0px',
          justifyContent: 'right',
        },
        responsiveConfig: {
          baseFontSize: 1,
          maxFontSize: 1,
          scaleWithImage: true,
        },
      },
    ],
  },
];
